@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules\baseline-browser-mapping\dist\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules\baseline-browser-mapping\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules\baseline-browser-mapping\dist\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules\baseline-browser-mapping\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\baseline-browser-mapping@2.8.7\node_modules;C:\Proyectos\lbcdev-web-components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\baseline-browser-mapping\dist\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\baseline-browser-mapping\dist\cli.js" %*
)
