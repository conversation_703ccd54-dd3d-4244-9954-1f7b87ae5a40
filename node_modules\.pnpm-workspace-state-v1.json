{"lastValidatedTimestamp": 1758916543278, "projects": {"C:\\Proyectos\\lbcdev-web-components": {"name": "lbcdev-web-components", "version": "1.0.0"}, "C:\\Proyectos\\lbcdev-web-components\\packages\\Atoms\\Button\\Animated\\astro": {"name": "@luinux81/astro-button-animated", "version": "1.0.0"}, "C:\\Proyectos\\lbcdev-web-components\\packages\\Atoms\\Button\\Base\\astro": {"name": "@luinux81/astro-button-base", "version": "1.0.1"}, "C:\\Proyectos\\lbcdev-web-components\\packages\\Atoms\\Button\\Icon\\astro": {"name": "@luinux81/astro-button-icon", "version": "1.0.0"}, "C:\\Proyectos\\lbcdev-web-components\\sites\\astro-catalog": {"name": "astro-catalog", "version": "0.0.1"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/Atoms/*/*/*", "packages/Molecules/*/*/*", "packages/Organisms/*/*/*", "packages/Templates/*/*/*", "packages/Bundles/*/*/*", "sites/*"]}, "filteredInstall": false}