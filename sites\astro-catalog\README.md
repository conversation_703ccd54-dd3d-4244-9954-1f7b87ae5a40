# 🧩 Astro Catalog - Catálogo de Componentes LBCDev

Catálogo interactivo de componentes web siguiendo la metodología **Atomic Design**, construido con Astro y TailwindCSS v4.

## 🚀 Características

### ✨ Diseño y UX

-  **Interfaz moderna** con diseño limpio y profesional
-  **Navegación sticky** con indicadores visuales del estado activo
-  **Scroll suave** entre secciones con JavaScript nativo
-  **Responsive design** optimizado para móviles, tablets y desktop
-  **Iconografía consistente** usando Material Symbols

### 📐 Organización por Atomic Design

-  **⚛️ Átomos**: Elementos básicos e indivisibles (botones, inputs, iconos)
-  **🧩 Moléculas**: Combinaciones de átomos (formularios, cards)
-  **🏗️ Organismos**: Componentes complejos (headers, modales, sidebars)
-  **📐 Templates**: Layouts de página completos

### 🎯 Estado de Componentes

-  **Badges de estado**: Disponible, En desarrollo, Próximamente
-  **Ejemplos interactivos** para componentes disponibles
-  **Placeholders visuales** para componentes en desarrollo
-  **Documentación inline** con ejemplos de uso

## 🛠️ Tecnologías

-  **Astro** - Framework de desarrollo
-  **TailwindCSS v4** - Estilos y diseño
-  **TypeScript** - Tipado estático
-  **Material Symbols** - Iconografía
-  **Inter Font** - Tipografía

## 📦 Componentes Incluidos

### ⚛️ Átomos Disponibles

-  **ButtonBase** ✅ - Botón básico con múltiples variantes (primary, secondary, danger, success)

### ⚛️ Átomos En Desarrollo

-  **ButtonIcon** 🚧 - Botón con iconos de Material Symbols
-  **ButtonAnimated** 🚧 - Botón con animaciones (hamburger, plus, arrow, dots)

### 🏗️ Organismos Disponibles

-  **Modal System** ✅ - Sistema completo de modales con auto-inicialización

### 🧩 Moléculas Planificadas

-  **Search Form** 📋 - Input + Button de búsqueda
-  **Product Card** 📋 - Imagen + Título + Precio
-  **Alert Message** 📋 - Icono + Mensaje + Acción

### 🏗️ Organismos Planificados

-  **Header Navigation** 📋 - Logo + Menú + Acciones
-  **Footer** 📋 - Links + Info + Social
-  **Sidebar** 📋 - Navegación lateral

### 📐 Templates Planificados

-  **Blog Layout** 📋 - Header + Content + Sidebar
-  **Dashboard** 📋 - Sidebar + Main + Widgets
-  **E-commerce** 📋 - Header + Products + Footer

## 🚀 Desarrollo

### Iniciar el servidor de desarrollo

```bash
cd sites/astro-catalog
npm run dev
```

### Estructura del proyecto

```
sites/astro-catalog/
├── src/
│   ├── pages/
│   │   └── index.astro          # Página principal del catálogo
│   └── styles/
│       └── global.css           # Estilos globales con TailwindCSS v4
├── public/
├── astro.config.mjs             # Configuración de Astro
└── package.json
```

## 🎨 Características de Diseño

### Paleta de Colores

-  **Primary**: Basado en `oklch(81.439% 0.1395 193.066)` con variantes automáticas
-  **Grays**: Escala completa de grises para texto y fondos
-  **Status Colors**: Verde (disponible), Amarillo (en desarrollo), Azul (información)

### Componentes de UI

-  **Cards**: Fondo blanco con sombras sutiles y bordes redondeados
-  **Badges**: Indicadores de estado con colores semánticos
-  **Navigation**: Tabs con indicadores visuales y hover states
-  **Placeholders**: Elementos con bordes punteados para componentes futuros

### Interactividad

-  **Navegación suave**: Scroll automático entre secciones
-  **Estados activos**: Indicadores visuales en la navegación
-  **Hover effects**: Transiciones suaves en elementos interactivos
-  **Responsive**: Adaptación automática a diferentes tamaños de pantalla

## 📝 Próximas Mejoras

1. **Implementar componentes faltantes** (ButtonIcon, ButtonAnimated)
2. **Agregar más ejemplos interactivos** para cada componente
3. **Crear página de documentación** detallada para cada componente
4. **Implementar búsqueda** de componentes
5. **Agregar modo oscuro**
6. **Incluir playground interactivo** para probar componentes
7. **Generar documentación automática** desde los componentes

## 🔗 Enlaces

-  [Repositorio Principal](https://github.com/Luinux81/lbcdev-web-components)
-  [Documentación](../docs/index.md)
-  [Guía de Atomic Design](../docs/concepts/atomic-design.md)

## 🧞 Comandos

Todos los comandos se ejecutan desde la raíz del proyecto:

| Comando           | Acción                                               |
| :---------------- | :--------------------------------------------------- |
| `npm install`     | Instala las dependencias                             |
| `npm run dev`     | Inicia el servidor de desarrollo en `localhost:4321` |
| `npm run build`   | Construye el sitio para producción en `./dist/`      |
| `npm run preview` | Previsualiza la build localmente                     |
