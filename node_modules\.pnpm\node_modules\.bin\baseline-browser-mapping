#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules/baseline-browser-mapping/dist/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules/baseline-browser-mapping/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules/baseline-browser-mapping/dist/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules/baseline-browser-mapping/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/baseline-browser-mapping@2.8.7/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../baseline-browser-mapping/dist/cli.js" "$@"
else
  exec node  "$basedir/../baseline-browser-mapping/dist/cli.js" "$@"
fi
