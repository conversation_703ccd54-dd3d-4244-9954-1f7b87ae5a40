#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules/vite/bin/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules/vite/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules/vite/bin/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules/vite/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/vite@6.3.6_@types+node@24.5_5c5aff3a7f1befeecf8edd074b0efd28/node_modules:/mnt/c/Proyectos/lbcdev-web-components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../vite/bin/vite.js" "$@"
fi
