hoistPattern:
  - '*'
hoistedDependencies:
  '@astrojs/compiler@2.13.0':
    '@astrojs/compiler': private
  '@astrojs/internal-helpers@0.7.3':
    '@astrojs/internal-helpers': private
  '@astrojs/markdown-remark@6.3.7':
    '@astrojs/markdown-remark': private
  '@astrojs/prism@3.3.0':
    '@astrojs/prism': private
  '@astrojs/telemetry@3.3.0':
    '@astrojs/telemetry': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.4':
    '@babel/parser': private
  '@babel/types@7.28.4':
    '@babel/types': private
  '@capsizecss/unpack@2.4.0':
    '@capsizecss/unpack': private
  '@esbuild/win32-x64@0.25.10':
    '@esbuild/win32-x64': private
  '@img/colour@1.0.0':
    '@img/colour': private
  '@img/sharp-win32-x64@0.34.4':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.31':
    '@jridgewell/trace-mapping': private
  '@oslojs/encoding@1.1.0':
    '@oslojs/encoding': private
  '@rollup/pluginutils@5.3.0(rollup@4.52.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-win32-x64-gnu@4.52.2':
    '@rollup/rollup-win32-x64-gnu': private
  '@rollup/rollup-win32-x64-msvc@4.52.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@shikijs/core@3.13.0':
    '@shikijs/core': private
  '@shikijs/engine-javascript@3.13.0':
    '@shikijs/engine-javascript': private
  '@shikijs/engine-oniguruma@3.13.0':
    '@shikijs/engine-oniguruma': private
  '@shikijs/langs@3.13.0':
    '@shikijs/langs': private
  '@shikijs/themes@3.13.0':
    '@shikijs/themes': private
  '@shikijs/types@3.13.0':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.13':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.13':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.13':
    '@tailwindcss/oxide': private
  '@tailwindcss/vite@4.1.13(vite@6.3.6(@types/node@24.5.2)(jiti@2.6.0)(lightningcss@1.30.1)(yaml@2.8.1))':
    '@tailwindcss/vite': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/fontkit@2.0.8':
    '@types/fontkit': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/nlcst@2.0.3':
    '@types/nlcst': private
  '@types/node@24.5.2':
    '@types/node': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  acorn@8.15.0:
    acorn: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-regex@6.2.2:
    ansi-regex: private
  ansi-styles@6.2.3:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-iterate@2.0.1:
    array-iterate: private
  astro@5.14.1(@types/node@24.5.2)(jiti@2.6.0)(lightningcss@1.30.1)(rollup@4.52.2)(typescript@5.9.2)(yaml@2.8.1):
    astro: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  base-64@1.0.0:
    base-64: private
  base64-js@1.5.1:
    base64-js: private
  baseline-browser-mapping@2.8.7:
    baseline-browser-mapping: private
  blob-to-buffer@1.2.9:
    blob-to-buffer: private
  boxen@8.0.1:
    boxen: private
  brotli@1.3.3:
    brotli: private
  browserslist@4.26.2:
    browserslist: private
  camelcase@8.0.0:
    camelcase: private
  caniuse-lite@1.0.30001745:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@5.6.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  ci-info@4.3.0:
    ci-info: private
  cli-boxes@3.0.0:
    cli-boxes: private
  clone@2.1.2:
    clone: private
  clsx@2.1.1:
    clsx: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  common-ancestor-path@1.0.1:
    common-ancestor-path: private
  cookie-es@1.2.2:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  cross-fetch@3.2.0:
    cross-fetch: private
  crossws@0.3.5:
    crossws: private
  css-tree@3.1.0:
    css-tree: private
  cssesc@3.0.0:
    cssesc: private
  debug@4.4.3:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  defu@6.1.4:
    defu: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.1.1:
    detect-libc: private
  deterministic-object-hash@2.0.2:
    deterministic-object-hash: private
  devalue@5.3.2:
    devalue: private
  devlop@1.1.0:
    devlop: private
  dfa@1.2.0:
    dfa: private
  diff@5.2.0:
    diff: private
  dlv@1.1.3:
    dlv: private
  dset@3.1.4:
    dset: private
  electron-to-chromium@1.5.224:
    electron-to-chromium: private
  emoji-regex@10.5.0:
    emoji-regex: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@6.0.1:
    entities: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.25.10:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@5.0.0:
    escape-string-regexp: private
  estree-walker@3.0.3:
    estree-walker: private
  eventemitter3@5.0.1:
    eventemitter3: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  flattie@1.1.1:
    flattie: private
  fontace@0.3.0:
    fontace: private
  fontkit@2.0.4:
    fontkit: private
  fraction.js@4.3.7:
    fraction.js: private
  get-east-asian-width@1.4.0:
    get-east-asian-width: private
  github-slugger@2.0.0:
    github-slugger: private
  graceful-fs@4.2.11:
    graceful-fs: private
  h3@1.15.4:
    h3: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  html-escaper@3.0.3:
    html-escaper: private
  html-void-elements@3.0.0:
    html-void-elements: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  import-meta-resolve@4.2.0:
    import-meta-resolve: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-docker@3.0.0:
    is-docker: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-wsl@3.1.0:
    is-wsl: private
  jiti@2.6.0:
    jiti: private
  js-yaml@4.1.0:
    js-yaml: private
  kleur@4.1.5:
    kleur: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.19:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  markdown-table@3.0.4:
    markdown-table: private
  mdast-util-definitions@6.0.0:
    mdast-util-definitions: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.1.0:
    minizlib: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  neotraverse@0.6.18:
    neotraverse: private
  nlcst-to-string@4.0.0:
    nlcst-to-string: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-mock-http@1.0.3:
    node-mock-http: private
  node-releases@2.0.21:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  oniguruma-parser@0.12.1:
    oniguruma-parser: private
  oniguruma-to-es@4.3.3:
    oniguruma-to-es: private
  p-limit@6.2.0:
    p-limit: private
  p-queue@8.1.1:
    p-queue: private
  p-timeout@6.1.4:
    p-timeout: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  pako@0.2.9:
    pako: private
  parse-latin@7.0.0:
    parse-latin: private
  parse5@7.3.0:
    parse5: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prismjs@1.30.0:
    prismjs: private
  prompts@2.4.2:
    prompts: private
  property-information@7.1.0:
    property-information: private
  radix3@1.1.2:
    radix3: private
  readdirp@4.1.2:
    readdirp: private
  regex-recursion@6.0.2:
    regex-recursion: private
  regex-utilities@2.3.0:
    regex-utilities: private
  regex@6.0.1:
    regex: private
  rehype-parse@9.0.1:
    rehype-parse: private
  rehype-raw@7.0.0:
    rehype-raw: private
  rehype-stringify@10.0.1:
    rehype-stringify: private
  rehype@13.0.2:
    rehype: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-smartypants@3.0.2:
    remark-smartypants: private
  remark-stringify@11.0.0:
    remark-stringify: private
  restructure@3.0.2:
    restructure: private
  retext-latin@4.0.0:
    retext-latin: private
  retext-smartypants@6.2.0:
    retext-smartypants: private
  retext-stringify@4.0.0:
    retext-stringify: private
  retext@9.0.0:
    retext: private
  rollup@4.52.2:
    rollup: private
  semver@7.7.2:
    semver: private
  sharp@0.34.4:
    sharp: private
  shiki@3.13.0:
    shiki: private
  sisteransi@1.0.5:
    sisteransi: private
  smol-toml@1.4.2:
    smol-toml: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.2:
    strip-ansi: private
  tailwindcss@4.1.13:
    tailwindcss: private
  tapable@2.2.3:
    tapable: private
  tar@7.5.1:
    tar: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.15:
    tinyglobby: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  tsconfck@3.1.6(typescript@5.9.2):
    tsconfck: private
  tslib@2.8.1:
    tslib: private
  type-fest@4.41.0:
    type-fest: private
  typescript@5.9.2:
    typescript: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@7.12.0:
    undici-types: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unified@11.0.5:
    unified: private
  unifont@0.5.2:
    unifont: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-modify-children@4.0.0:
    unist-util-modify-children: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-children@3.0.0:
    unist-util-visit-children: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unstorage@1.17.1:
    unstorage: private
  update-browserslist-db@1.1.3(browserslist@4.26.2):
    update-browserslist-db: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vite@6.3.6(@types/node@24.5.2)(jiti@2.6.0)(lightningcss@1.30.1)(yaml@2.8.1):
    vite: private
  vitefu@1.1.1(vite@6.3.6(@types/node@24.5.2)(jiti@2.6.0)(lightningcss@1.30.1)(yaml@2.8.1)):
    vitefu: private
  web-namespaces@2.0.1:
    web-namespaces: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-pm-runs@1.1.0:
    which-pm-runs: private
  widest-line@5.0.0:
    widest-line: private
  wrap-ansi@9.0.2:
    wrap-ansi: private
  xxhash-wasm@1.1.0:
    xxhash-wasm: private
  yallist@5.0.0:
    yallist: private
  yaml@2.8.1:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yocto-spinner@0.2.3:
    yocto-spinner: private
  yoctocolors@2.1.2:
    yoctocolors: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
  zod-to-ts@1.2.0(typescript@5.9.2)(zod@3.25.76):
    zod-to-ts: private
  zod@3.25.76:
    zod: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.17.1
pendingBuilds: []
prunedAt: Fri, 26 Sep 2025 17:12:44 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  '@lbcdev': https://npm.pkg.github.com/
  '@luinux81': https://npm.pkg.github.com/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.5.0'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.10'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.10'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.10'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.10'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.10'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.10'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.10'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.10'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.10'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.10'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.10'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.10'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.10'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.10'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.10'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.10'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.10'
  - '@esbuild/netbsd-arm64@0.25.10'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.10'
  - '@esbuild/openbsd-arm64@0.25.10'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.10'
  - '@esbuild/openharmony-arm64@0.25.10'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.10'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.10'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.10'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.4'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.4'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.2.3'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.2.3'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.2.3'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.2.3'
  - '@img/sharp-libvips-linux-ppc64@1.2.3'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.2.3'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.2.3'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.3'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.3'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.4'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.4'
  - '@img/sharp-linux-ppc64@0.34.4'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.4'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.4'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.4'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.4'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.4'
  - '@img/sharp-win32-arm64@0.34.4'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.4'
  - '@rollup/rollup-android-arm-eabi@4.52.2'
  - '@rollup/rollup-android-arm64@4.52.2'
  - '@rollup/rollup-darwin-arm64@4.52.2'
  - '@rollup/rollup-darwin-x64@4.52.2'
  - '@rollup/rollup-freebsd-arm64@4.52.2'
  - '@rollup/rollup-freebsd-x64@4.52.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.52.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.52.2'
  - '@rollup/rollup-linux-arm64-gnu@4.52.2'
  - '@rollup/rollup-linux-arm64-musl@4.52.2'
  - '@rollup/rollup-linux-loong64-gnu@4.52.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.52.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.52.2'
  - '@rollup/rollup-linux-riscv64-musl@4.52.2'
  - '@rollup/rollup-linux-s390x-gnu@4.52.2'
  - '@rollup/rollup-linux-x64-gnu@4.52.2'
  - '@rollup/rollup-linux-x64-musl@4.52.2'
  - '@rollup/rollup-openharmony-arm64@4.52.2'
  - '@rollup/rollup-win32-arm64-msvc@4.52.2'
  - '@rollup/rollup-win32-ia32-msvc@4.52.2'
  - '@tailwindcss/oxide-android-arm64@4.1.13'
  - '@tailwindcss/oxide-darwin-arm64@4.1.13'
  - '@tailwindcss/oxide-darwin-x64@4.1.13'
  - '@tailwindcss/oxide-freebsd-x64@4.1.13'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.13'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.13'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.13'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.13'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.13'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.13'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.13'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Proyectos\lbcdev-web-components\node_modules\.pnpm
virtualStoreDirMaxLength: 60
