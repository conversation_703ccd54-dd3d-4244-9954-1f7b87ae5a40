---
import "../styles/global.css";

// ⚛️ Átomos - Importaciones
import ButtonBase from "@luinux81/astro-button-base";
// import ButtonIcon from "@luinux81/astro-button-icon";
// import ButtonAnimated from "@luinux81/astro-button-animated";

// 🧩 Moléculas - Importaciones (cuando estén disponibles)
// import SearchForm from "@luinux81/astro-search-form";
// import ProductCard from "@luinux81/astro-product-card";

// 🏗️ Organismos - Importaciones (cuando estén disponibles)
// import Header from "@luinux81/astro-header";
// import Footer from "@luinux81/astro-footer";

// 📐 Templates - Importaciones (cuando estén disponibles)
// import BlogLayout from "@luinux81/astro-blog-layout";

// Datos de ejemplo para demostrar componentes
const sampleData = {
	buttons: [
		{ label: "Primario", variant: "primary" },
		{ label: "Secundario", variant: "secondary" },
		{ label: "Peligro", variant: "danger" },
		{ label: "Éxito", variant: "success" },
	],
	icons: [
		{ icon: "home", label: "Inicio" },
		{ icon: "search", label: "Buscar" },
		{ icon: "settings", label: "Configuración" },
		{ icon: "person", label: "Perfil" },
	],
	animations: [
		{ type: "hamburger", label: "Menú Hamburguesa" },
		{ type: "plus", label: "Agregar/Cerrar" },
		{ type: "arrow", label: "Flecha" },
		{ type: "dots", label: "Puntos" },
	],
};
---

<!doctype html>
<html lang="es">
	<head>
		<title>🧩 LBCDev - Catálogo de Componentes</title>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta
			name="description"
			content="Catálogo interactivo de componentes web siguiendo la metodología Atomic Design"
		/>

		<!-- Material Symbols y Google Fonts -->
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
		/>
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
			rel="stylesheet"
		/>
	</head>
	<body class="bg-gray-50 font-sans">
		<!-- Header del Catálogo -->
		<header class="bg-white shadow-sm border-b border-gray-200">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
				<div class="flex items-center justify-between">
					<div>
						<h1 class="text-3xl font-bold text-gray-900">
							🧩 LBCDev Web Components
						</h1>
						<p class="text-gray-600 mt-2">
							Catálogo interactivo siguiendo la metodología <strong
								>Atomic Design</strong
							>
						</p>
					</div>
					<div class="flex items-center space-x-4">
						<span
							class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800"
						>
							Astro
						</span>
						<span
							class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
						>
							TailwindCSS v4
						</span>
					</div>
				</div>
			</div>
		</header>

		<!-- Navegación por niveles -->
		<nav class="bg-white border-b border-gray-200 sticky top-0 z-10">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex space-x-8">
					<a
						href="#atoms"
						class="border-b-2 border-primary-500 py-4 px-1 text-sm font-medium text-primary-600"
					>
						⚛️ Átomos
					</a>
					<a
						href="#molecules"
						class="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
					>
						🧩 Moléculas
					</a>
					<a
						href="#organisms"
						class="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
					>
						🏗️ Organismos
					</a>
					<a
						href="#templates"
						class="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
					>
						� Templates
					</a>
				</div>
			</div>
		</nav>

		<!-- Contenido Principal -->
		<main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<!-- ⚛️ ÁTOMOS -->
			<section id="atoms" class="mb-16">
				<div class="mb-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-2">⚛️ Átomos</h2>
					<p class="text-gray-600">
						Elementos básicos e indivisibles de la interfaz. No pueden
						descomponerse en partes más pequeñas.
					</p>
				</div>

				<!-- Botones Base -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Button Base
						</h3>
						<p class="text-sm text-gray-600">
							Botón básico con múltiples variantes de estilo
						</p>
					</div>
					<div class="p-6">
						<div
							class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
						>
							{
								sampleData.buttons.map((button) => (
									<div class="flex flex-col items-center space-y-2">
										<ButtonBase
											label={button.label}
											variant={button.variant}
										/>
										<code class="text-xs text-gray-500">
											variant="{button.variant}"
										</code>
									</div>
								))
							}
						</div>
						<div class="mt-4 p-4 bg-gray-50 rounded-md">
							<p class="text-sm text-gray-700 font-medium mb-2">Uso:</p>
							<code class="text-sm text-gray-600">
								&lt;ButtonBase label="Mi Botón" variant="primary" /&gt;
							</code>
						</div>
					</div>
				</div>

				<!-- Botones con Iconos -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Button Icon
						</h3>
						<p class="text-sm text-gray-600">
							Botón con soporte para iconos de Material Symbols
						</p>
						<span
							class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mt-2"
						>
							En desarrollo
						</span>
					</div>
					<div class="p-6">
						<div
							class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
						>
							<div class="text-gray-400 mb-4">
								<span class="material-symbols-outlined text-6xl"
									>smart_button</span
								>
							</div>
							<h4 class="font-medium text-gray-900 mb-2">Button Icon</h4>
							<p class="text-sm text-gray-500 mb-4">
								Botón con iconos de Material Symbols integrados
							</p>
							<div class="p-4 bg-gray-50 rounded-md">
								<p class="text-sm text-gray-700 font-medium mb-2">
									Uso previsto:
								</p>
								<code class="text-sm text-gray-600">
									&lt;ButtonIcon icon="home" label="Inicio" /&gt;
								</code>
							</div>
						</div>
					</div>
				</div>

				<!-- Botones Animados -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Button Animated
						</h3>
						<p class="text-sm text-gray-600">
							Botón con animaciones interactivas (hamburger, plus, arrow,
							dots)
						</p>
						<span
							class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mt-2"
						>
							En desarrollo
						</span>
					</div>
					<div class="p-6">
						<div
							class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
						>
							<div class="text-gray-400 mb-4">
								<span class="material-symbols-outlined text-6xl"
									>animation</span
								>
							</div>
							<h4 class="font-medium text-gray-900 mb-2">
								Button Animated
							</h4>
							<p class="text-sm text-gray-500 mb-4">
								Botones con animaciones interactivas: hamburger, plus,
								arrow, dots
							</p>
							<div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
								{
									sampleData.animations.map((anim) => (
										<div class="text-center">
											<div class="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
												<span class="material-symbols-outlined text-gray-400">
													{anim.type === "hamburger"
														? "menu"
														: anim.type === "plus"
															? "add"
															: anim.type === "arrow"
																? "arrow_forward"
																: "more_horiz"}
												</span>
											</div>
											<span class="text-xs text-gray-600">
												{anim.label}
											</span>
										</div>
									))
								}
							</div>
							<div class="p-4 bg-gray-50 rounded-md">
								<p class="text-sm text-gray-700 font-medium mb-2">
									Uso previsto:
								</p>
								<code class="text-sm text-gray-600">
									&lt;ButtonAnimated animation="hamburger"
									toggleOnClick /&gt;
								</code>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- 🧩 MOLÉCULAS -->
			<section id="molecules" class="mb-16">
				<div class="mb-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-2">
						🧩 Moléculas
					</h2>
					<p class="text-gray-600">
						Combinaciones de dos o más átomos que funcionan como una
						unidad para cumplir un objetivo específico.
					</p>
				</div>

				<!-- Placeholder para futuras moléculas -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Próximamente
						</h3>
						<p class="text-sm text-gray-600">Moléculas en desarrollo</p>
					</div>
					<div class="p-6">
						<div
							class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
						>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>search</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Search Form
								</h4>
								<p class="text-sm text-gray-500">
									Input + Button de búsqueda
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>credit_card</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Product Card
								</h4>
								<p class="text-sm text-gray-500">
									Imagen + Título + Precio
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>notifications</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Alert Message
								</h4>
								<p class="text-sm text-gray-500">
									Icono + Mensaje + Acción
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- 🏗️ ORGANISMOS -->
			<section id="organisms" class="mb-16">
				<div class="mb-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-2">
						🏗️ Organismos
					</h2>
					<p class="text-gray-600">
						Componentes complejos que integran átomos y/o moléculas para
						formar secciones completas de la interfaz.
					</p>
				</div>

				<!-- Modal System (Disponible) -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Modal System
						</h3>
						<p class="text-sm text-gray-600">
							Sistema completo de modales con auto-inicialización
						</p>
						<span
							class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 mt-2"
						>
							Disponible
						</span>
					</div>
					<div class="p-6">
						<div class="bg-gray-50 rounded-md p-4 mb-4">
							<p class="text-sm text-gray-700 font-medium mb-2">
								Características:
							</p>
							<ul class="text-sm text-gray-600 space-y-1">
								<li>• Auto-inicialización automática</li>
								<li>
									• Múltiples tipos: Contacto, Servicio, Proyecto,
									Feature
								</li>
								<li>• Gestión de estado avanzada</li>
								<li>• Animaciones suaves</li>
							</ul>
						</div>
						<div class="p-4 bg-gray-50 rounded-md">
							<p class="text-sm text-gray-700 font-medium mb-2">Uso:</p>
							<code class="text-sm text-gray-600">
								import &#123;ModalContacto&#125; from
								'@lbcdev/modal-system-astro';<br />
								&lt;ModalContacto id="citaModal" /&gt;
							</code>
						</div>
					</div>
				</div>

				<!-- Placeholder para futuros organismos -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Próximamente
						</h3>
						<p class="text-sm text-gray-600">Organismos en desarrollo</p>
					</div>
					<div class="p-6">
						<div
							class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
						>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>menu</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Header Navigation
								</h4>
								<p class="text-sm text-gray-500">
									Logo + Menú + Acciones
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>footer</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">Footer</h4>
								<p class="text-sm text-gray-500">
									Links + Info + Social
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>sidebar</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">Sidebar</h4>
								<p class="text-sm text-gray-500">Navegación lateral</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- 📐 TEMPLATES -->
			<section id="templates" class="mb-16">
				<div class="mb-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-2">
						📐 Templates
					</h2>
					<p class="text-gray-600">
						Plantillas de página que organizan organismos en un layout sin
						incluir contenido final.
					</p>
				</div>

				<!-- Placeholder para futuros templates -->
				<div
					class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8"
				>
					<div class="px-6 py-4 border-b border-gray-200">
						<h3 class="text-lg font-semibold text-gray-900">
							Próximamente
						</h3>
						<p class="text-sm text-gray-600">Templates en desarrollo</p>
					</div>
					<div class="p-6">
						<div
							class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
						>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>article</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Blog Layout
								</h4>
								<p class="text-sm text-gray-500">
									Header + Content + Sidebar
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>dashboard</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									Dashboard
								</h4>
								<p class="text-sm text-gray-500">
									Sidebar + Main + Widgets
								</p>
							</div>
							<div
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"
							>
								<div class="text-gray-400 mb-2">
									<span class="material-symbols-outlined text-4xl"
										>storefront</span
									>
								</div>
								<h4 class="font-medium text-gray-900 mb-1">
									E-commerce
								</h4>
								<p class="text-sm text-gray-500">
									Header + Products + Footer
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>
		</main>

		<!-- Footer del Catálogo -->
		<footer class="bg-white border-t border-gray-200 mt-16">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div class="flex flex-col md:flex-row justify-between items-center">
					<div class="mb-4 md:mb-0">
						<p class="text-gray-600">
							<strong>LBCDev Web Components</strong> - Biblioteca siguiendo
							Atomic Design
						</p>
						<p class="text-sm text-gray-500 mt-1">
							Construido con Astro + TailwindCSS v4 + TypeScript
						</p>
					</div>
					<div class="flex items-center space-x-4">
						<a
							href="https://github.com/Luinux81/lbcdev-web-components"
							class="text-gray-500 hover:text-gray-700 transition-colors"
						>
							<span class="material-symbols-outlined">code</span>
						</a>
						<a
							href="/docs"
							class="text-gray-500 hover:text-gray-700 transition-colors"
						>
							<span class="material-symbols-outlined">description</span>
						</a>
					</div>
				</div>
			</div>
		</footer>

		<!-- JavaScript para navegación suave -->
		<script>
			// Navegación suave entre secciones
			document.addEventListener("DOMContentLoaded", function () {
				const navLinks = document.querySelectorAll('nav a[href^="#"]');

				navLinks.forEach((link) => {
					link.addEventListener("click", function (e) {
						e.preventDefault();
						const target = e.target as HTMLAnchorElement;
						const targetId = target.getAttribute("href")?.substring(1);
						const targetElement = targetId
							? document.getElementById(targetId)
							: null;

						if (targetElement) {
							// Actualizar estado activo de navegación
							navLinks.forEach((l) => {
								l.classList.remove(
									"border-primary-500",
									"text-primary-600"
								);
								l.classList.add("border-transparent", "text-gray-500");
							});

							target.classList.remove(
								"border-transparent",
								"text-gray-500"
							);
							target.classList.add(
								"border-primary-500",
								"text-primary-600"
							);

							// Scroll suave
							targetElement.scrollIntoView({
								behavior: "smooth",
								block: "start",
							});
						}
					});
				});

				// Actualizar navegación al hacer scroll
				const sections = document.querySelectorAll("section[id]");
				const observer = new IntersectionObserver(
					(entries) => {
						entries.forEach((entry) => {
							if (entry.isIntersecting) {
								const id = entry.target.getAttribute("id");
								const correspondingLink = document.querySelector(
									`nav a[href="#${id}"]`
								);

								if (correspondingLink) {
									navLinks.forEach((l) => {
										l.classList.remove(
											"border-primary-500",
											"text-primary-600"
										);
										l.classList.add(
											"border-transparent",
											"text-gray-500"
										);
									});

									correspondingLink.classList.remove(
										"border-transparent",
										"text-gray-500"
									);
									correspondingLink.classList.add(
										"border-primary-500",
										"text-primary-600"
									);
								}
							}
						});
					},
					{
						threshold: 0.3,
						rootMargin: "-100px 0px -50% 0px",
					}
				);

				sections.forEach((section) => {
					observer.observe(section);
				});
			});
		</script>
	</body>
</html>
