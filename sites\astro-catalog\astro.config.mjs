// @ts-check
import { defineConfig } from "astro/config";
import path, { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

import tailwindcss from "@tailwindcss/vite";

// https://astro.build/config
export default defineConfig({
	vite: {
		server: {
			watch: {
				followSymlinks: true,
			},
		},
		// optimizeDeps: {
		// 	exclude: ["@luinux81/astro-button-base"],
		// },
		resolve: {
			alias: {
				"@/": `${path.resolve(__dirname, "src")}/`,
			},
		},
		plugins: [tailwindcss()],
	},
});
