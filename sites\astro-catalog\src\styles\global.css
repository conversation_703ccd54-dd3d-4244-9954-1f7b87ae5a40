@import "tailwindcss";

@source "../../node_modules/@luinux81/astro-button-base/";

@theme {
  /* Fuentes */
  /* --font-header: "Montserrat", sans-serif;
  --font-body: "Open Sans", sans-serif; */

  /* Variable base */
  --color-primary: oklch(81.439% 0.1395 193.066);
  
  /* Paleta generada automáticamente usando funciones de Tailwind */
  --color-primary-50: oklch(from var(--color-primary) calc(l + 0.30) calc(c * 0.3) h);
  --color-primary-100: oklch(from var(--color-primary) calc(l + 0.25) calc(c * 0.4) h);
  --color-primary-200: oklch(from var(--color-primary) calc(l + 0.20) calc(c * 0.5) h);
  --color-primary-300: oklch(from var(--color-primary) calc(l + 0.15) calc(c * 0.6) h);
  --color-primary-400: oklch(from var(--color-primary) calc(l + 0.08) calc(c * 0.8) h);
  --color-primary-500: var(--color-primary);
  --color-primary-600: oklch(from var(--color-primary) calc(l - 0.08) calc(c * 1.1) h);
  --color-primary-700: oklch(from var(--color-primary) calc(l - 0.15) calc(c * 1.2) h);
  --color-primary-800: oklch(from var(--color-primary) calc(l - 0.25) calc(c * 1.3) h);
  --color-primary-900: oklch(from var(--color-primary) calc(l - 0.35) calc(c * 1.4) h);
  --color-primary-950: oklch(from var(--color-primary) calc(l - 0.45) calc(c * 1.5) h);
}